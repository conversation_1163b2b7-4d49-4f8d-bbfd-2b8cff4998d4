<template>
    <YSXK-ScaleDiv :reduceW="0" :reduceH="0" :w="1920" :h="1080">
        <div class="largeScreen" ref="largeScreen">
            <img class="title"      src="../../../assets/images/largeScreen/title.png" />
            <img class="leftJump"   :src="LCR === 'left'   ? leftJump_c   : leftJump"   @click="jumpTemplate('left')" />
            <img class="centerJump" :src="LCR === 'center' ? centerJump_c : centerJump" @click="jumpTemplate('center')" />
            <img class="rightJump"  :src="LCR === 'right'  ? rightJump_c  : rightJump"  @click="jumpTemplate('right')" />

            <!-- 数据可视化 -->
            <left   v-if     ="LCR === 'left'" />
            <!-- 数字孪生 -->
            <center v-if     ="LCR === 'center'" />
            <!-- 调度中心 -->
            <right  v-else-if="LCR === 'right'" />

            <img class="bottom" src="../../../assets/images/largeScreen/bottom.png" />
            <img class="exit"   src="../../../assets/images/largeScreen/exit.png" @click="exitScreen" v-if="isFullScreen" />
            <img class="open"   src="../../../assets/images/largeScreen/open.png" @click="fullScreen" v-else />
            <a class="technicalSupport" href="http://yunsxk.com/" target="_blank">技术支持 - 重庆云昇新控智能科技有限公司</a>
        </div>
    </YSXK-ScaleDiv>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useDynamicScale } from '/@/hooks/web/useDynamicScale'

import leftJump     from '../../../assets/images/largeScreen/leftJump.png'
import leftJump_c   from '../../../assets/images/largeScreen/leftJump_c.png'
import rightJump    from '../../../assets/images/largeScreen/rightJump.png'
import rightJump_c  from '../../../assets/images/largeScreen/rightJump_c.png'
import centerJump   from '../../../assets/images/largeScreen/centerJump.png'
import centerJump_c from '../../../assets/images/largeScreen/centerJump_c.png'

import left from './components/left.vue'
import center from './components/center.vue'
import right from './components/right.vue'
import YSXKScaleDiv from '@/views/dashboard/Analysis/components/YSXK-ScaleDiv.vue';

// 获取动态缩放值
const { dynamicReduceW, dynamicW } = useDynamicScale()

const LCR = ref('left')

const jumpTemplate = (lr: string) => {
    LCR.value = lr
    if (!isFullScreen.value) {
        fullScreen()
    }
}

const largeScreen = ref()

const isFullScreen = ref(false)

const fullScreen = () => {
    largeScreen.value.requestFullscreen()
}

const exitScreen = () => {
    document.exitFullscreen()
}

document.addEventListener('fullscreenchange', (e) => {
    if (document.fullscreenElement) {
        isFullScreen.value = true
    } else {
        isFullScreen.value = false
    }
})
</script>

<style lang="less" scoped>
.largeScreen {
    width: 100%;
    height: 100%;
    position: relative;
    background-color: #ccc;
    // overflow: hidden;
    img {
        position: absolute;
        user-select: none;
    }
    .title {
        width: 1920px;
        height: 105px;
        top: 0;
        left: 0;
        z-index: 10;
    }
    .centerJump, .leftJump, .rightJump {
        width: calc(320 / 1920 * 100%);
        height: 56px;
        top: 22px;
        cursor: pointer;
        z-index: 10;
    }
    .leftJump {
        left: calc(320 / 1920 * 100%);
    }
    .centerJump {
        width: calc(400 / 1920 * 100%);
        height: 46px;
        left: 50%;
        top: 74px;
        transform: translateX(-50%);
    }
    .rightJump {
        right: calc(320 / 1920 * 100%);
    }
    .bottom {
        width: 100%;
        height: 46px;
        left: 0;
        bottom: 0;
        // position: fixed;
    }
    .open, .exit {
        width: 97px;
        height: 40px;
        cursor: pointer;
        right: 24%;
        bottom: 60px;
        position: fixed;
    }
    .technicalSupport {
        position: absolute;
        left: 50%;
        bottom: 9px;
        transform: translateX(-50%);
        font-size: 14px;
        color: #FFFFFF;
        line-height: 20px;
        text-shadow: 0px -1px 10px #0143FF;
    }
}
</style>
